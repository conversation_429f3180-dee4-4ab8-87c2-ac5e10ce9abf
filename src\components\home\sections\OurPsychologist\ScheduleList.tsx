import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>B<PERSON> } from '@/components/ui/scroll-area'
import moment from 'moment-timezone'
import 'moment/locale/id'
import { useRouter } from 'next/navigation'
import { useBooking } from '@/context/useBookingCounseling'

type TimeSlot = {
  time: string
  dateTimeWithTimezone: string
  isAvailable: boolean
}

type ScheduleDay = {
  date: string
  timezone: string
  times: TimeSlot[]
}

type Availability = {
  durationInMinute: number
  price: number
  schedule: ScheduleDay[]
}

type ScheduleItem = {
  label: string
  dateISO: string
  timeOnly: string
}

type ScheduleListProps = {
  breakdownAvailability?: Availability[]
  className?: string
  maxItems?: number
  psychologistId?: string
}

export const formatSchedule = (breakdownAvailability?: Availability[], maxItems = 20) => {
  if (!breakdownAvailability?.length || !breakdownAvailability[0]?.schedule) return []

  moment.locale('id')

  const availableSlots = breakdownAvailability[0].schedule
    .flatMap((day) =>
      day.times
        .filter((slot) => slot.isAvailable)
        .map((slot) => ({
          date: day.date,
          time: slot.time,
          dateTime: moment.parseZone(slot.dateTimeWithTimezone),
          dateTimeWithTimezone: slot.dateTimeWithTimezone,
        }))
    )
    .filter((slot) => slot.dateTime.isAfter(moment()))
    .sort((a, b) => a.dateTime.valueOf() - b.dateTime.valueOf())
    .slice(0, maxItems)
    .map((slot) => {
      const date = moment(slot.dateTime)
      const isToday = date.isSame(moment(), 'day')
      const isTomorrow = date.isSame(moment().add(1, 'day'), 'day')

      const dateLabel = isToday ? 'Hari ini' : isTomorrow ? 'Besok' : date.format('D MMM')
      const timeLabel = date.format('HH:mm')

      return {
        label: `${dateLabel}, ${timeLabel} WIB`,
        dateISO: slot.dateTime.format('YYYY-MM-DD'),
        timeOnly: timeLabel,
      }
    })

  return availableSlots
}

export const ScheduleList = ({
  breakdownAvailability,
  className = '',
  maxItems = 20,
  psychologistId,
}: ScheduleListProps) => {
  const router = useRouter()
  const { setBookingDetails } = useBooking()

  const schedules = formatSchedule(breakdownAvailability, maxItems)

  if (!schedules.length) return null

  const handleScheduleClick = (schedule: ScheduleItem) => {
    // Get price from availability if exists
    let price = 149000 // Default price
    let duration = 60 // Default duration

    if (breakdownAvailability && breakdownAvailability.length > 0) {
      duration = breakdownAvailability[0].durationInMinute
      price = breakdownAvailability[0].price
    }

    // Create a date object from the schedule's date and time
    const selectedDate = schedule.dateISO
    const selectedTime = schedule.timeOnly

    // Store booking initial details in context
    setBookingDetails({
      psychologistId: psychologistId || '',
      psychologistName: '', // Will be filled on detail page
      psychologistImage: '',
      specializations: '',
      selectedDate,
      selectedTime,
      formattedDate: schedule.label,
      rawSchedule: moment(`${selectedDate}T${selectedTime}`).toISOString(),
      duration,
      method: 'VideoCall',
      location: 'Online',
      price,
      discount: 0,
      voucherPromo: 0,
      totalAfterDiscount: price,
      finalPrice: price,
    })

    // Navigate to psychologist detail page
    router.push(`/detail-psychologist/${psychologistId}`)
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className="text-body-sm font-semibold text-gray-900 shrink-0">Jadwal</span>
      <ScrollArea className="w-full">
        <div className="flex gap-2 pr-4">
          {schedules.map((schedule, index) => (
            <div
              key={index}
              className="flex-none rounded-sm border border-gray-300 px-2 py-1 bg-white cursor-pointer hover:bg-gray-50 transition-colors"
              onClick={() => handleScheduleClick(schedule)}
            >
              <span className="text-xs text-gray-800 whitespace-nowrap">{schedule.label}</span>
            </div>
          ))}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  )
}
