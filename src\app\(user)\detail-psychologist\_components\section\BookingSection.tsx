'use client'
import { useState, useEffect } from 'react'
import { SVGIcons, IIcons } from '@/components/_common/icon'
import { isToday, isTomorrow } from 'date-fns'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { usePathname, useRouter } from 'next/navigation'
import { Card, H2 } from '@/components/_common/ui'
import { Button } from '@/components/ui/button'
import { useBooking } from '@/context/useBookingCounseling'

// Define types
interface BreakdownAvailability {
  durationInMinute: number
  price: number
  schedule?: ScheduleItem[]
}

interface ScheduleItem {
  date: string
  timezone: string
  times: TimeSlot[]
}

interface TimeSlot {
  time: string
  dateTimeWithTimezone: string
  isAvailable: boolean
}

interface Psychologist {
  id: string
  fullName: string
  profilePhoto?: string
  nickname?: string
  specialization?: string[]
  breakdownAvailability?: BreakdownAvailability[]
}

interface FormattedDate {
  day: string
  date: string
  month: string
  fullDate: string
}

export interface BookingSectionProps {
  user: any
  psychologist: Psychologist
  availabilityDate: string[] | null
}

export default function BookingSection({ psychologist, availabilityDate, user }: BookingSectionProps) {
  const [selectedDuration, setSelectedDuration] = useState<number>(60)
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedTime, setSelectedTime] = useState('')
  const [formattedDates, setFormattedDates] = useState<FormattedDate[]>([])
  const [availableTimes, setAvailableTimes] = useState<string[]>([])
  const [selectedMedia, setSelectedMedia] = useState<'Call' | 'VideoCall'>('VideoCall')
  const router = useRouter()
  const pathname = usePathname()
  const { bookingState, setBookingDetails } = useBooking()

  useEffect(() => {
    if (availabilityDate && availabilityDate.length > 0) {
      const days = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu']
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des']

      const formatted = availabilityDate.map((dateStr: string, index: number) => {
        const date = new Date(dateStr)
        const day = date.getDay()
        const dayName =
          index === 0 && isToday(date) ? 'Hari ini' : index === 0 && isTomorrow(date) ? 'Besok' : days[day]

        return {
          day: dayName,
          date: date.getDate().toString(),
          month: months[date.getMonth()],
          fullDate: dateStr,
        }
      })

      setFormattedDates(formatted)

      // If we have a booking state with a selected date, use that
      // Otherwise, select first date by default
      if (bookingState?.selectedDate) {
        // Find the matching date in our formatted dates
        const matchingDate = formatted.find((d) => d.fullDate.startsWith(bookingState.selectedDate))
        if (matchingDate) {
          setSelectedDate(matchingDate.fullDate)
        } else if (formatted.length > 0) {
          setSelectedDate(formatted[0].fullDate)
        }
      } else if (formatted.length > 0) {
        setSelectedDate(formatted[0].fullDate)
      }
    }
  }, [availabilityDate, bookingState])

  // Effect for available times - Updated to use breakdownAvailability data
  useEffect(() => {
    if (selectedDate && psychologist.breakdownAvailability) {
      const selectedDurationSchedule = psychologist.breakdownAvailability.find(
        (breakdown) => breakdown.durationInMinute === selectedDuration
      )

      if (selectedDurationSchedule?.schedule) {
        const scheduleForDate = selectedDurationSchedule.schedule.find(
          (schedule) => schedule.date === selectedDate.split('T')[0]
        )

        if (scheduleForDate?.times) {
          // Filter only available times and format them
          const availableTimeSlots = scheduleForDate.times
            .filter((slot) => slot.isAvailable)
            .map((slot) => `${slot.time} WIB`)

          setAvailableTimes(availableTimeSlots)

          // Reset selected time if previously selected time is no longer available
          if (!availableTimeSlots.find((time) => time.startsWith(selectedTime))) {
            setSelectedTime(availableTimeSlots[0]?.split(' ')[0] || '')
          }
        } else {
          setAvailableTimes([])
          setSelectedTime('')
        }
      } else {
        setAvailableTimes([])
        setSelectedTime('')
      }
    } else {
      setAvailableTimes([])
      setSelectedTime('')
    }
  }, [selectedDate, selectedDuration, psychologist.breakdownAvailability])

  // Set duration from booking state if available
  useEffect(() => {
    if (bookingState?.duration) {
      setSelectedDuration(bookingState.duration)
    }
  }, [bookingState])

  // Helper function to calculate end time
  const getEndTime = (startTime: string, durationMinutes: number) => {
    const [hours, minutes] = startTime.split(':').map(Number)

    let endMinutes = minutes + durationMinutes
    let endHours = hours + Math.floor(endMinutes / 60)
    endMinutes = endMinutes % 60

    return `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`
  }

  const handleBooking = () => {
    if (!user) {
      return router.push('/auth/login')
    }

    // Format the date and time for display
    const selectedDateObj = new Date(selectedDate)
    const dayNames = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu']
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des']

    // Ensure durationInMinutes is a number
    const durationInMinutes =
      typeof selectedDuration === 'string' ? parseInt(selectedDuration, 10) : selectedDuration

    const formattedDate = `${dayNames[selectedDateObj.getDay()]}, ${selectedDateObj.getDate()} ${
      monthNames[selectedDateObj.getMonth()]
    } ${selectedDateObj.getFullYear()}, ${selectedTime}-${getEndTime(selectedTime, durationInMinutes)} WIB (GMT+7)`

    // Calculate price based on selected duration
    let price = 149000 // Default price for 60 minutes
    if (psychologist.breakdownAvailability && psychologist.breakdownAvailability.length > 0) {
      const selectedOption = psychologist.breakdownAvailability.find(
        (option) => option.durationInMinute === durationInMinutes
      )
      if (selectedOption) {
        price = selectedOption.price
      }
    } else if (durationInMinutes === 120) {
      price = 299000 // Default price for 120 minutes
    }

    // Apply discounts - These would normally come from your application state or API
    const discount = 0
    const voucherPromo = 0
    const totalAfterDiscount = price - discount - voucherPromo
    const finalPrice = totalAfterDiscount
    // Prepare specializations for display
    const specializations = psychologist.specialization || []
    const formattedSpecializations =
      specializations.length <= 3
        ? specializations.join(', ')
        : `${specializations.slice(0, 3).join(', ')}, +${specializations.length - 3} lainnya`

    // Create ISO format for schedule time
    // Convert selected time from "HH:MM WIB" to ISO format using selectedDate as the base
    const [timeHours, timeMinutes] = selectedTime.split(':').map(Number)
    const scheduleDate = new Date(selectedDate)
    scheduleDate.setHours(timeHours, timeMinutes, 0, 0)
    const rawSchedule = scheduleDate.toISOString()

    setBookingDetails({
      psychologistId: psychologist.id || '',
      psychologistName: psychologist.fullName,
      psychologistImage: psychologist.profilePhoto || '',
      specializations: formattedSpecializations,
      selectedDate,
      selectedTime,
      formattedDate,
      rawSchedule: rawSchedule, // Properly formatted ISO string
      duration: durationInMinutes, // As a number
      method: selectedMedia,
      location: 'Online',
      price,
      discount,
      voucherPromo,
      totalAfterDiscount,
      finalPrice,
    })

    // Navigate to the konseling page
    router.push(`${pathname}/konseling`)
  }

  return (
    <div className="p-4 border border-gray-100 rounded-lg">
      <H2 className="text-lg font-semibold mb-4">Durasi Konseling</H2>
      {/* Duration Options */}
      <div className="flex gap-2 mb-6">
        {psychologist.breakdownAvailability &&
          psychologist.breakdownAvailability.map((breakdown, index) => (
            <button
              key={index}
              className={`flex-1 py-2 px-3 border rounded-md text-center font-semibold ${
                selectedDuration === breakdown.durationInMinute
                  ? 'border-main-100 bg-main-50'
                  : 'border-gray-100'
              }`}
              onClick={() => setSelectedDuration(breakdown.durationInMinute)}
            >
              <div className="font-semibold">{breakdown.durationInMinute} Menit</div>
              <div className="text-sm text-gray-500">Rp{breakdown.price.toLocaleString('id-ID')}</div>
            </button>
          ))}
        {(!psychologist.breakdownAvailability || psychologist.breakdownAvailability.length === 0) && (
          <>
            <Button
              className={`flex-1 py-2 px-3 border rounded-md text-center ${
                selectedDuration === 60 ? 'border-main-100 bg-main-50' : 'border-gray-100'
              }`}
              onClick={() => setSelectedDuration(60)}
            >
              <div>60 Menit</div>
              <div className="text-sm text-gray-500">Rp149.000</div>
            </Button>
            <Button
              variant={'outline'}
              className={`flex-1 py-2 px-3 border rounded-md text-center ${
                selectedDuration === 120 ? 'border-main-100 bg-main-50' : 'border-gray-100'
              }`}
              onClick={() => setSelectedDuration(120)}
            >
              <div>120 Menit</div>
              <div className="text-sm text-gray-500">Rp299.000</div>
            </Button>
          </>
        )}
      </div>

      {/* Date option */}
      <H2 className="text-lg font-semibold mb-4">Tanggal</H2>
      <div className="flex gap-2 mb-6 overflow-x-auto max-w-64 md:max-w-screen-md xs:max-w-screen-xs sm:max-w-screen-sm">
        {formattedDates.length > 0 ? (
          formattedDates.map((date) => (
            <button
              key={date.fullDate}
              className={`flex flex-col items-center min-w-16 border rounded-md w-full ${
                selectedDate === date.fullDate ? 'border-main-100 bg-main-50' : 'border-gray-100'
              }`}
              onClick={() => setSelectedDate(date.fullDate)}
            >
              <div className="text-sm flex flex-col items-center w-full">
                <span
                  className={`text-xs md:text-sm w-full text-center py-1 rounded-t-md font-semibold ${
                    selectedDate === date.fullDate ? 'bg-main-100 text-white' : 'border-b-gray-100 border'
                  }`}
                >
                  {date.day}
                </span>
                <span className="font-semibold">{date.date}</span>
              </div>
              <div className="text-xs text-gray-500">{date.month}</div>
            </button>
          ))
        ) : (
          <div className="text-sm text-gray-500">Tidak ada tanggal yang tersedia</div>
        )}
        <button className="border rounded-md p-2 flex-shrink-0">
          <SVGIcons name={IIcons.Calendar} className="text-main-100" />
        </button>
      </div>

      {/* Time Selection */}
      <h3 className="font-semibold mb-2">Waktu</h3>
      <div className="grid grid-cols-3 gap-2 mb-6">
        {availableTimes.length > 0 ? (
          availableTimes.map((time) => (
            <button
              key={time}
              className={`py-2 px-3 border rounded-md text-center text-xs font-semibold ${
                selectedTime === time.split(' ')[0] ? 'border-main-100 bg-main-50' : 'border-gray-100'
              }`}
              onClick={() => setSelectedTime(time.split(' ')[0])}
            >
              {time}
            </button>
          ))
        ) : (
          <div className="col-span-3 text-center text-sm text-gray-500">
            {selectedDate
              ? 'Tidak ada jadwal tersedia untuk durasi yang dipilih'
              : 'Silakan pilih tanggal terlebih dahulu'}
          </div>
        )}
      </div>

      {/* Media Selection */}
      <h3 className="font-semibold mb-2">Media Konseling</h3>
      <div className="flex gap-2 mb-6">
        <button
          className={`flex-1 flex items-center justify-center gap-2 py-3 px-2 md:px-4 border rounded-md ${
            selectedMedia === 'Call' ? 'border-main-100 text-main-100 bg-main-50' : 'border-gray-100'
          }`}
          onClick={() => setSelectedMedia('Call')}
        >
          <SVGIcons name={IIcons.Call} className="text-main-100" />
          <div className="flex flex-col items-start">
            <span className="text-sm font-semibold">Voice Call</span>
            <span className="text-[10px] text-gray-500">Via GoogleMeet</span>
          </div>
        </button>
        <button
          className={`flex-1 flex items-center justify-center gap-2 py-3 px-2 md:px-4 border rounded-md ${
            selectedMedia === 'VideoCall' ? 'border-main-100 text-main-100 bg-main-50' : 'border-gray-100'
          }`}
          onClick={() => setSelectedMedia('VideoCall')}
        >
          <SVGIcons name={IIcons.Video} className="text-main-100" />
          <div className="flex flex-col items-start">
            <span className="text-sm font-semibold">Video Call</span>
            <span className="text-[10px] text-gray-500">Via GoogleMeet</span>
          </div>
        </button>
      </div>

      {/* Book Button */}
      <ButtonPrimary
        variant="contained"
        className="w-full py-3 rounded-md font-semibold hover:bg-main-600 transition-colors"
        onClick={handleBooking}
        disabled={!selectedTime}
      >
        Buat Jadwal
      </ButtonPrimary>
    </div>
  )
}
